import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../../src/lib/axios';
import { API_ENDPOINT } from '../../../../src/data/api-endpoints';
import { handleBackendError } from '../../../../src/utils/handle-backend-error';
import { transformBulkOperationRequest, transformBulkOperationResponse } from '../../../../src/requests/admin-users';
import { getJwt } from '../../../../src/utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { token } = await getJwt(req);
    try {
      console.log('Bulk reject request body:', req.body);
      const transformedData = transformBulkOperationRequest(req.body);
      console.log('Transformed bulk reject data:', transformedData);

      const response = await BACKEND_API.post(API_ENDPOINT.users.bulkReject, transformedData, { 
        headers: { Authorization: token } 
      });
      console.log('Backend bulk reject response:', JSON.stringify(response.data, null, 2));
      const data = transformBulkOperationResponse(response.data);
      res.status(200).json(data);
    } catch (error: any) {
      console.error('Error in bulk reject operation:', error);

      // Use the proper error handling utility
      const errorKey = handleBackendError(error, []);

      // Return structured error response
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || 'Failed to bulk reject users';

      res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ message: 'Method not allowed' });
  }
}
