/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-api-error';

import {
  UserListParamsType,
  UserStatusChangeRequestType,
  UserApprovalRequestType,
  ApprovalRequestType,
  ComprehensiveUserManagementRequestType,
  BulkOperationRequestType,
  UserListResponseType,
  UserDetailResponseType,
  UserStatusChangeResponseType,
  UserApprovalResponseType,
  OperatorApprovalResponseType,
  ComprehensiveUserManagementResponseType,
  BulkOperationResponseType,
} from './types';

// API Request Functions
export const getUserListRequest = async (params: UserListParamsType = {}): Promise<UserListResponseType> => {
  try {
    const response = await CLIENT_API.get(API_ENDPOINT.users.list, { params });

    // Transform the API response to match the expected frontend structure
    const apiResponse = response.data;

    return {
      success: apiResponse.success,
      data: {
        users: Array.isArray(apiResponse.data) ? apiResponse.data : [],
        total: apiResponse.pagination?.total,
        pagination: apiResponse.pagination,
      },
    };
  } catch (error: any) {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      const { signOut } = await import('next-auth/react');
      await signOut({ redirect: false });
      window.location.href = '/auth/login';
    }
    throw handleApiError(error);
  }
};

export const getUserDetailRequest = async (userId: string): Promise<UserDetailResponseType> => {
  try {
    // Use the dedicated detail endpoint
    const response = await CLIENT_API.get(API_ENDPOINT.users.detail(userId), {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    // Handle the response from the dedicated detail endpoint
    const apiResponse = response.data;

    if (!apiResponse.success || !apiResponse.data?.user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    return {
      success: apiResponse.success,
      data: {
        user: apiResponse.data.user,
      },
    };
  } catch (error: any) {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      const { signOut } = await import('next-auth/react');
      await signOut({ redirect: false });
      window.location.href = '/auth/login';
    }
    throw handleApiError(error);
  }
};

export const changeUserStatusRequest = async (data: UserStatusChangeRequestType): Promise<UserStatusChangeResponseType> => {
  try {
    const response = await CLIENT_API.put(API_ENDPOINT.users.status, data);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const approveUserRequest = async (data: UserApprovalRequestType): Promise<UserApprovalResponseType> => {
  try {
    const response = await CLIENT_API.put(API_ENDPOINT.users.approval, data);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const changeOperatorApprovalRequest = async (data: ApprovalRequestType): Promise<OperatorApprovalResponseType> => {
  try {
    const response = await CLIENT_API.post(API_ENDPOINT.users.approval, data);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const comprehensiveUserManagementRequest = async (data: ComprehensiveUserManagementRequestType): Promise<ComprehensiveUserManagementResponseType> => {
  try {
    const response = await CLIENT_API.put('/admin/users/management', data);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const bulkApproveUsersRequest = async (data: BulkOperationRequestType): Promise<BulkOperationResponseType> => {
  try {
    const response = await CLIENT_API.post('/admin/users/bulk-approve', data);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const bulkRejectUsersRequest = async (data: BulkOperationRequestType): Promise<BulkOperationResponseType> => {
  try {
    const response = await CLIENT_API.post('/admin/users/bulk-reject', data);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

// React Query Configuration Objects
export const getUserListQuery = (params: UserListParamsType = {}) => ({
  queryKey: ['admin-users-list', params],
  queryFn: () => getUserListRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 2 * 60 * 1000, // 2 minutes
  retry: (failureCount: number, error: any) => {
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
});

export const getUserDetailQuery = (userId: string) => ({
  queryKey: ['admin-user-detail', userId], // Clean key for dedicated endpoint
  queryFn: () => getUserDetailRequest(userId),
  refetchOnWindowFocus: false,
  staleTime: 0, // Always consider data stale for user details
  gcTime: 0, // Don't cache the data
  enabled: !!userId,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
});

export const changeUserStatusMutation = {
  mutationKey: ['admin-change-user-status'],
  mutationFn: changeUserStatusRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const approveUserMutation = {
  mutationKey: ['admin-approve-user'],
  mutationFn: approveUserRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const changeOperatorApprovalMutation = {
  mutationKey: ['admin-change-operator-approval'],
  mutationFn: changeOperatorApprovalRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const comprehensiveUserManagementMutation = {
  mutationKey: ['admin-comprehensive-user-management'],
  mutationFn: comprehensiveUserManagementRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const bulkApproveUsersMutation = {
  mutationKey: ['admin-bulk-approve-users'],
  mutationFn: bulkApproveUsersRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const bulkRejectUsersMutation = {
  mutationKey: ['admin-bulk-reject-users'],
  mutationFn: bulkRejectUsersRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};
