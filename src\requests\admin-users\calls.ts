/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-api-error';

import {
  transformUserListParams,
  transformUserStatusChangeRequest,
  transformUserApprovalRequest,
  transformApprovalRequest,
  transformComprehensiveUserManagementRequest,
  transformBulkOperationRequest,
} from './request-transformer';
import {
  transformUserListResponse,
  transformUserDetailResponse,
  transformUserStatusChangeResponse,
  transformUserApprovalResponse,
  transformOperatorApprovalResponse,
  transformComprehensiveUserManagementResponse,
  transformBulkOperationResponse,
} from './response-transformer';
import {
  UserListParamsType,
  UserStatusChangeRequestType,
  UserApprovalRequestType,
  ApprovalRequestType,
  ComprehensiveUserManagementRequestType,
  BulkOperationRequestType,
  UserListResponseType,
  UserDetailResponseType,
  UserStatusChangeResponseType,
  UserApprovalResponseType,
  OperatorApprovalResponseType,
  ComprehensiveUserManagementResponseType,
  BulkOperationResponseType,
} from './types.d';

// API Request Functions
export const getUserListRequest = async (params: UserListParamsType = {}): Promise<UserListResponseType> => {
  try {
    const transformedParams = transformUserListParams(params);
    const response = await CLIENT_API.get(API_ENDPOINT.users.list, { params: transformedParams });
    return transformUserListResponse(response.data);
  } catch (error: any) {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      const { signOut } = await import('next-auth/react');
      await signOut({ redirect: false });
      window.location.href = '/auth/login';
    }
    throw handleApiError(error);
  }
};

export const getUserDetailRequest = async (userId: string): Promise<UserDetailResponseType> => {
  try {
    // Use the dedicated detail endpoint
    const response = await CLIENT_API.get(API_ENDPOINT.users.detail(userId), {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    return transformUserDetailResponse(response.data);
  } catch (error: any) {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      const { signOut } = await import('next-auth/react');
      await signOut({ redirect: false });
      window.location.href = '/auth/login';
    }
    throw handleApiError(error);
  }
};

export const changeUserStatusRequest = async (data: UserStatusChangeRequestType): Promise<UserStatusChangeResponseType> => {
  try {
    const transformedData = transformUserStatusChangeRequest(data);
    const response = await CLIENT_API.put(API_ENDPOINT.users.status, transformedData);
    return transformUserStatusChangeResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const approveUserRequest = async (data: UserApprovalRequestType): Promise<UserApprovalResponseType> => {
  try {
    const transformedData = transformUserApprovalRequest(data);
    const response = await CLIENT_API.put(API_ENDPOINT.users.approval, transformedData);
    return transformUserApprovalResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const changeOperatorApprovalRequest = async (data: ApprovalRequestType): Promise<OperatorApprovalResponseType> => {
  try {
    const transformedData = transformApprovalRequest(data);
    const response = await CLIENT_API.post(API_ENDPOINT.users.approval, transformedData);
    return transformOperatorApprovalResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const comprehensiveUserManagementRequest = async (data: ComprehensiveUserManagementRequestType): Promise<ComprehensiveUserManagementResponseType> => {
  try {
    const transformedData = transformComprehensiveUserManagementRequest(data);
    const response = await CLIENT_API.put('/admin/users/management', transformedData);
    return transformComprehensiveUserManagementResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const bulkApproveUsersRequest = async (data: BulkOperationRequestType): Promise<BulkOperationResponseType> => {
  try {
    const transformedData = transformBulkOperationRequest(data);
    const response = await CLIENT_API.post('/admin/users/bulk-approve', transformedData);
    return transformBulkOperationResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const bulkRejectUsersRequest = async (data: BulkOperationRequestType): Promise<BulkOperationResponseType> => {
  try {
    const transformedData = transformBulkOperationRequest(data);
    const response = await CLIENT_API.post('/admin/users/bulk-reject', transformedData);
    return transformBulkOperationResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

// React Query Configuration Objects
export const getUserListQuery = (params: UserListParamsType = {}) => ({
  queryKey: ['admin-users-list', params],
  queryFn: () => getUserListRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 2 * 60 * 1000, // 2 minutes
  retry: (failureCount: number, error: any) => {
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
});

export const getUserDetailQuery = (userId: string) => ({
  queryKey: ['admin-user-detail', userId], // Clean key for dedicated endpoint
  queryFn: () => getUserDetailRequest(userId),
  refetchOnWindowFocus: false,
  staleTime: 0, // Always consider data stale for user details
  gcTime: 0, // Don't cache the data
  enabled: !!userId,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
});

export const changeUserStatusMutation = {
  mutationKey: ['admin-change-user-status'],
  mutationFn: changeUserStatusRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const approveUserMutation = {
  mutationKey: ['admin-approve-user'],
  mutationFn: approveUserRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const changeOperatorApprovalMutation = {
  mutationKey: ['admin-change-operator-approval'],
  mutationFn: changeOperatorApprovalRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const comprehensiveUserManagementMutation = {
  mutationKey: ['admin-comprehensive-user-management'],
  mutationFn: comprehensiveUserManagementRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const bulkApproveUsersMutation = {
  mutationKey: ['admin-bulk-approve-users'],
  mutationFn: bulkApproveUsersRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const bulkRejectUsersMutation = {
  mutationKey: ['admin-bulk-reject-users'],
  mutationFn: bulkRejectUsersRequest,
  retry: (failureCount: number, error: any) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};
