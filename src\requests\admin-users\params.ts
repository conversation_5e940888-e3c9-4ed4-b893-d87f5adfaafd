import { NextApiRequest } from 'next';

// eslint-disable-next-line complexity
export const returnUserListParams = (req: NextApiRequest) => {
  const params = req.query;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const cleanParams: Record<string, any> = {};

  // Handle pagination
  if (params.page) {
    cleanParams.page = params.page;
  }
  if (params.limit) {
    cleanParams.limit = params.limit;
  }
  if (params.pageSize) {
    cleanParams.limit = params.pageSize; // Map pageSize to limit
  }

  // Handle search
  if (params.search) {
    cleanParams.search = params.search;
  }

  // Handle filters
  if (params.user_type) {
    cleanParams.user_type = params.user_type;
  }
  if (params.status) {
    cleanParams.status = params.status;
  }
  if (params.approval_status) {
    cleanParams.approval_status = params.approval_status;
  }

  // Handle sorting
  if (params.sort) {
    cleanParams.sort = params.sort;
  }

  // Handle detail flag for user detail requests
  if (params.detail) {
    cleanParams.detail = params.detail;
  }

  // Handle user ID for detail requests
  if (params.id) {
    cleanParams.id = params.id;
  }

  // Add cache buster for fresh data
  if (params._t) {
    cleanParams._t = params._t;
  }

  return cleanParams;
};

// This map is used to convert front-end keys to back-end keys for the sort parameter
export const sortKeysMapping = new Map<string, string>([
  ['name', 'name'],
  ['email', 'email'],
  ['userType', 'user_type'],
  ['status', 'status'],
  ['approvalStatus', 'approval_status'],
  ['createdAt', 'created_at'],
  ['updatedAt', 'updated_at'],
  ['lastLogin', 'last_login'],
]);

// Helper function to transform sort parameter using the mapping
export const transformSortParam = (sort: string): string => {
  if (!sort) return sort;
  
  // Handle descending sort (starts with -)
  const isDescending = sort.startsWith('-');
  const sortKey = isDescending ? sort.substring(1) : sort;
  
  // Map the sort key if it exists in our mapping
  const mappedKey = sortKeysMapping.get(sortKey) || sortKey;
  
  // Return with proper prefix
  return isDescending ? `-${mappedKey}` : mappedKey;
};
