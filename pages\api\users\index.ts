/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { API_ENDPOINT } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { handleBackendError } from '../../../src/utils/handle-backend-error';
import axios from 'axios';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  // Only allow GET
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
    return;
  }

  const { token } = await getJwt(req);

  // Authentication guard
  if (!token) {
    res.status(401).json({
      success: false,
      error: {
        type: 'AUTHENTICATION_ERROR',
        message: 'Authentication token is missing or expired',
        code: 401,
      },
    });
    return;
  }

  try {
    const backendRes = await BACKEND_API.get(API_ENDPOINT.users.list, {
      params: req.query,
      headers: { Authorization: token },
    });

    res.status(200).json(backendRes.data);
  } catch (error: any) {
    // Handle token‑expiry from backend
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      res.status(401).json({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'Authentication token is invalid or expired',
          code: 401,
        },
      });
      return;
    }

    // Other back‑end errors
    const errorKey = handleBackendError(error, []);
    const statusCode = axios.isAxiosError(error) && error.response?.status
      ? error.response.status
      : 500;

    // Prefer explicit .message or nested .error.message
    const errorMessage = axios.isAxiosError(error) && error.response?.data
      ? (error.response.data as any).message
          ?? (error.response.data as any).error?.message
          ?? 'Failed to fetch user list'
      : 'Failed to fetch user list';

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: errorKey,
    });
  }
}
