import axios from 'axios';
import { getSession } from 'next-auth/react';

const API_BASE_URL = process.env.BACKEND_API_URL || 'http://localhost:8000';

const CLIENT_API = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  },
});

const BACKEND_API = axios.create({
  baseURL: `${API_BASE_URL}/api/admin`,
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  },
});

// Add request interceptor to attach authentication token
BACKEND_API.interceptors.request.use(
  async (config) => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      try {
        const session = await getSession();
        if (session?.accessToken) {
          // eslint-disable-next-line no-param-reassign
          config.headers.Authorization = `Bearer ${session.accessToken}`;
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error getting session for API request:', error);
      }
    }
    return config;
  },
  (error) => Promise.reject(error),
);

// Add response interceptor to handle authentication errors globally
CLIENT_API.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 401 errors globally
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      // Import signOut dynamically to avoid SSR issues
      const { signOut } = await import('next-auth/react');

      // Sign out the user
      await signOut({ redirect: false });

      // Redirect to login page
      window.location.href = '/auth/login';

      return Promise.reject(error);
    }

    return Promise.reject(error);
  },
);

export { CLIENT_API, BACKEND_API };
