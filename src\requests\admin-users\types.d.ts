import { z } from 'zod';

// Zod schemas for request validation
export const userListParamsSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']).optional(),
  approval_status: z.enum(['APPROVED', 'PENDING', 'REJECTED']).optional(),
});

export const userStatusChangeRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']),
  reason: z.string().optional(),
});

export const userApprovalRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  approval_status: z.enum(['APPROVED']),
  notes: z.string().optional(),
});

// New comprehensive user management schemas
export const approvalRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  approved: z.boolean(),
  notes: z.string().optional(),
});

export const comprehensiveUserManagementRequestSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']).optional(),
  approved: z.boolean().optional(),
  reason: z.string().optional(),
  notes: z.string().optional(),
});

export const bulkOperationRequestSchema = z.object({
  userIds: z.array(z.string().min(1)).min(1, 'At least one user ID is required'),
  operation: z.enum(['approve', 'reject', 'activate', 'suspend']),
  reason: z.string().optional(),
  notes: z.string().optional(),
});

// Zod schemas for response validation
export const userProfileSchema = z.object({
  business_name: z.string().optional(),
  business_address: z.string().optional(),
  business_license: z.string().optional(),
  operating_hours: z.string().optional(),
});

export const userStatisticsSchema = z.object({
  total_shipments: z.number(),
  completed_shipments: z.number(),
  success_rate: z.number(),
});

export const userSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
  user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']),
  approval_status: z.enum(['APPROVED', 'PENDING', 'REJECTED']).optional(),
  created_at: z.string(),
  updated_at: z.string().optional(),
  last_login: z.string().nullable().optional(),
  profile: userProfileSchema.optional(),
  statistics: userStatisticsSchema.optional(),
  accessOperator: z.object({
    approved: z.boolean(),
    business_name: z.string().optional(),
    address: z.string().optional(),
  }).nullable().optional(),
  carOperator: z.object({
    approved: z.boolean(),
    business_name: z.string().optional(),
    address: z.string().optional(),
  }).nullable().optional(),
});

export const paginationInfoSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

export const userListResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    users: z.array(userSchema),
    total: z.number().optional(),
    pagination: paginationInfoSchema.optional(),
  }),
});

export const userDetailResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    user: userSchema,
  }),
});

export const userStatusChangeResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: z.object({
      id: z.string(),
      status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']),
    }),
  }),
});

export const userApprovalResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: z.object({
      id: z.string(),
      approval_status: z.enum(['APPROVED', 'PENDING', 'REJECTED']).optional(),
    }),
  }),
});

// New response schemas for comprehensive user management
export const approvalResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: z.object({
      id: z.string(),
      approved: z.boolean(),
    }),
  }),
});

export const comprehensiveUserManagementResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    user: z.object({
      id: z.string(),
      status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING']).optional(),
      approved: z.boolean().optional(),
    }),
  }),
});

export const bulkOperationResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    processed: z.number(),
    successful: z.number(),
    failed: z.number(),
    results: z.array(z.object({
      userId: z.string(),
      success: z.boolean(),
      message: z.string().optional(),
    })),
  }),
});

// Type inference from schemas
export type UserListParamsType = z.infer<typeof userListParamsSchema>;
export type UserStatusChangeRequestType = z.infer<typeof userStatusChangeRequestSchema>;
export type UserApprovalRequestType = z.infer<typeof userApprovalRequestSchema>;

// New request types
export type ApprovalRequestType = z.infer<typeof approvalRequestSchema>;
export type ComprehensiveUserManagementRequestType = z.infer<typeof comprehensiveUserManagementRequestSchema>;
export type BulkOperationRequestType = z.infer<typeof bulkOperationRequestSchema>;

export type UserSchemaType = z.infer<typeof userSchema>;
export type UserListResponseType = z.infer<typeof userListResponseSchema>;
export type UserDetailResponseType = z.infer<typeof userDetailResponseSchema>;
export type UserStatusChangeResponseType = z.infer<typeof userStatusChangeResponseSchema>;
export type UserApprovalResponseType = z.infer<typeof userApprovalResponseSchema>;

// New response types
export type OperatorApprovalResponseType = z.infer<typeof approvalResponseSchema>;
export type ComprehensiveUserManagementResponseType = z.infer<typeof comprehensiveUserManagementResponseSchema>;
export type BulkOperationResponseType = z.infer<typeof bulkOperationResponseSchema>;
