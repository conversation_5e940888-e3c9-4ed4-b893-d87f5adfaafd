/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextApiRequest, NextApiResponse } from 'next';
import { API_ENDPOINT } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { getJwt } from '../../../src/utils';
import { handleBackendError } from '../../../src/utils/handle-backend-error';
import { AxiosError } from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    res.setHeader('Allow', ['PUT']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
    return;
  }

  const { token } = await getJwt(req);

  try {
    const apiRes = await BACKEND_API.put(API_ENDPOINT.users.status, req.body, {
      headers: { Authorization: token },
    });

    // Return the actual data payload rather than the full Axios response object
    res.status(200).json(apiRes.data);
  } catch (err) {
    // Narrow to AxiosError to safely access response
    const error = err as AxiosError;

    // Use our backend‐error handler to get a key
    const errorKey = handleBackendError(error, []);

    // Determine status code & message
    const statusCode = error.response?.status ?? 500;
    const errorData = error.response?.data as any;
    const errorMessage = errorData?.message ?? errorData?.error?.message ?? 'Failed to change user status';

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: errorKey,
    });
  }
}
