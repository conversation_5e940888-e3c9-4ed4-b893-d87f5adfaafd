import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../../src/lib/axios';
import { API_ENDPOINT } from '../../../../src/data/api-endpoints';
import { handleBackendError } from '../../../../src/utils/handle-backend-error';
import { transformComprehensiveUserManagementRequest, transformComprehensiveUserManagementResponse } from '../../../../src/requests/admin-users';
import { getJwt } from '../../../../src/utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'PUT') {
    const { token } = await getJwt(req);
    try {
      console.log('Comprehensive user management request body:', req.body);
      const transformedData = transformComprehensiveUserManagementRequest(req.body);
      console.log('Transformed comprehensive user management data:', transformedData);

      const response = await BACKEND_API.put(API_ENDPOINT.users.management, transformedData, { 
        headers: { Authorization: token } 
      });
      console.log('Backend comprehensive user management response:', JSON.stringify(response.data, null, 2));
      const data = transformComprehensiveUserManagementResponse(response.data);
      res.status(200).json(data);
    } catch (error: any) {
      console.error('Error in comprehensive user management:', error);

      // Use the proper error handling utility
      const errorKey = handleBackendError(error, []);

      // Return structured error response
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || 'Failed to manage user';

      res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  } else {
    res.setHeader('Allow', ['PUT']);
    res.status(405).json({ message: 'Method not allowed' });
  }
}
