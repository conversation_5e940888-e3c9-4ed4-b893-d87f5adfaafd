import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data';
import { handleBackendError } from '../../../src/utils/handle-backend-error';
import { getJwt } from '../../../src/utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { token } = await getJwt(req);
    try {
      const response = await BACKEND_API.post(API_ENDPOINT.users.bulkApprove, req.body, {
        headers: { Authorization: token },
      });
      res.status(200).json(response.data);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // Use the proper error handling utility
      const errorKey = handleBackendError(error, []);

      // Return structured error response
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || 'Failed to bulk approve users';

      res.status(statusCode).json({
        success: false,
        error: {
          message: errorMessage,
          key: errorKey,
        },
      });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ message: 'Method not allowed' });
  }
}
