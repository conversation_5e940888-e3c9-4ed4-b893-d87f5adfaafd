import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data';
import { handleBackendError } from '../../../src/utils/handle-backend-error';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === 'PUT') {
    try {
      const response = await BACKEND_API.post(API_ENDPOINT.users.approval, req.body, { headers: { Authorization: token } });
      res.status(200).json(response.data);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || 'Failed to change user approval status';

      res.status(statusCode).json({
        success: false,
        message: errorMessage,
        error: errorKey,
      });
    }
  } else if (req.method === 'POST') {
    try {
      const response = await BACKEND_API.post(API_ENDPOINT.users.approval, req.body, { headers: { Authorization: token } });
      res.status(200).json(response.data);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const errorKey = handleBackendError(error, []);
      const statusCode = error.response?.status || 500;
      const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || 'Failed to change operator approval status';

      res.status(statusCode).json({
        success: false,
        message: errorMessage,
        error: errorKey,
      });
    }
  } else {
    res.setHeader('Allow', ['PUT', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
