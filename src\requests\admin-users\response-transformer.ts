import {
  userListResponseSchema,
  userDetailResponseSchema,
  userStatusChangeResponseSchema,
  userApprovalResponseSchema,
  approvalResponseSchema,
  comprehensiveUserManagementResponseSchema,
  bulkOperationResponseSchema,
  UserListResponseType,
  UserDetailResponseType,
  UserStatusChangeResponseType,
  UserApprovalResponseType,
  OperatorApprovalResponseType,
  ComprehensiveUserManagementResponseType,
  BulkOperationResponseType,
} from './types.d';

// Response transformers - convert backend data to frontend format
export const transformUserListResponse = (data: unknown): UserListResponseType => {
  const validated = userListResponseSchema.parse(data);
  return {
    success: validated.success,
    data: {
      users: validated.data.users.map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        approval_status: user.approval_status,
        created_at: user.created_at,
        updated_at: user.updated_at,
        last_login: user.last_login,
        profile: user.profile,
        statistics: user.statistics,
        accessOperator: user.accessOperator,
        carOperator: user.carOperator,
      })),
      total: validated.data.total,
      pagination: validated.data.pagination,
    },
  };
};

export const transformUserDetailResponse = (data: unknown): UserDetailResponseType => {
  const validated = userDetailResponseSchema.parse(data);
  return {
    success: validated.success,
    data: {
      user: {
        id: validated.data.user.id,
        name: validated.data.user.name,
        email: validated.data.user.email,
        phone: validated.data.user.phone,
        user_type: validated.data.user.user_type,
        status: validated.data.user.status,
        approval_status: validated.data.user.approval_status,
        created_at: validated.data.user.created_at,
        updated_at: validated.data.user.updated_at,
        last_login: validated.data.user.last_login,
        profile: validated.data.user.profile,
        statistics: validated.data.user.statistics,
        accessOperator: validated.data.user.accessOperator,
        carOperator: validated.data.user.carOperator,
      },
    },
  };
};

export const transformUserStatusChangeResponse = (data: unknown): UserStatusChangeResponseType => {
  const validated = userStatusChangeResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      user: {
        id: validated.data.user.id,
        status: validated.data.user.status,
      },
    },
  };
};

export const transformUserApprovalResponse = (data: unknown): UserApprovalResponseType => {
  const validated = userApprovalResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      user: {
        id: validated.data.user.id,
        approval_status: validated.data.user.approval_status,
      },
    },
  };
};

export const transformOperatorApprovalResponse = (data: unknown): OperatorApprovalResponseType => {
  const validated = approvalResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      user: {
        id: validated.data.user.id,
        approved: validated.data.user.approved,
      },
    },
  };
};

export const transformComprehensiveUserManagementResponse = (data: unknown): ComprehensiveUserManagementResponseType => {
  const validated = comprehensiveUserManagementResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      user: {
        id: validated.data.user.id,
        status: validated.data.user.status,
        approved: validated.data.user.approved,
      },
    },
  };
};

export const transformBulkOperationResponse = (data: unknown): BulkOperationResponseType => {
  const validated = bulkOperationResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      processed: validated.data.processed,
      successful: validated.data.successful,
      failed: validated.data.failed,
      results: validated.data.results.map((result) => ({
        userId: result.userId,
        success: result.success,
        message: result.message,
      })),
    },
  };
};
